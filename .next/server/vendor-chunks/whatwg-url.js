"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url";
exports.ids = ["vendor-chunks/whatwg-url"];
exports.modules = {

/***/ "(rsc)/./node_modules/whatwg-url/lib/URL-impl.js":
/*!*************************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL-impl.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\");\nexports.implementation = class URLImpl {\n    constructor(constructorArgs){\n        const url = constructorArgs[0];\n        const base = constructorArgs[1];\n        let parsedBase = null;\n        if (base !== undefined) {\n            parsedBase = usm.basicURLParse(base);\n            if (parsedBase === \"failure\") {\n                throw new TypeError(\"Invalid base URL\");\n            }\n        }\n        const parsedURL = usm.basicURLParse(url, {\n            baseURL: parsedBase\n        });\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    // TODO: query stuff\n    }\n    get href() {\n        return usm.serializeURL(this._url);\n    }\n    set href(v) {\n        const parsedURL = usm.basicURLParse(v);\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    }\n    get origin() {\n        return usm.serializeURLOrigin(this._url);\n    }\n    get protocol() {\n        return this._url.scheme + \":\";\n    }\n    set protocol(v) {\n        usm.basicURLParse(v + \":\", {\n            url: this._url,\n            stateOverride: \"scheme start\"\n        });\n    }\n    get username() {\n        return this._url.username;\n    }\n    set username(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setTheUsername(this._url, v);\n    }\n    get password() {\n        return this._url.password;\n    }\n    set password(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setThePassword(this._url, v);\n    }\n    get host() {\n        const url = this._url;\n        if (url.host === null) {\n            return \"\";\n        }\n        if (url.port === null) {\n            return usm.serializeHost(url.host);\n        }\n        return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n    }\n    set host(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"host\"\n        });\n    }\n    get hostname() {\n        if (this._url.host === null) {\n            return \"\";\n        }\n        return usm.serializeHost(this._url.host);\n    }\n    set hostname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"hostname\"\n        });\n    }\n    get port() {\n        if (this._url.port === null) {\n            return \"\";\n        }\n        return usm.serializeInteger(this._url.port);\n    }\n    set port(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        if (v === \"\") {\n            this._url.port = null;\n        } else {\n            usm.basicURLParse(v, {\n                url: this._url,\n                stateOverride: \"port\"\n            });\n        }\n    }\n    get pathname() {\n        if (this._url.cannotBeABaseURL) {\n            return this._url.path[0];\n        }\n        if (this._url.path.length === 0) {\n            return \"\";\n        }\n        return \"/\" + this._url.path.join(\"/\");\n    }\n    set pathname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        this._url.path = [];\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"path start\"\n        });\n    }\n    get search() {\n        if (this._url.query === null || this._url.query === \"\") {\n            return \"\";\n        }\n        return \"?\" + this._url.query;\n    }\n    set search(v) {\n        // TODO: query stuff\n        const url = this._url;\n        if (v === \"\") {\n            url.query = null;\n            return;\n        }\n        const input = v[0] === \"?\" ? v.substring(1) : v;\n        url.query = \"\";\n        usm.basicURLParse(input, {\n            url,\n            stateOverride: \"query\"\n        });\n    }\n    get hash() {\n        if (this._url.fragment === null || this._url.fragment === \"\") {\n            return \"\";\n        }\n        return \"#\" + this._url.fragment;\n    }\n    set hash(v) {\n        if (v === \"\") {\n            this._url.fragment = null;\n            return;\n        }\n        const input = v[0] === \"#\" ? v.substring(1) : v;\n        this._url.fragment = \"\";\n        usm.basicURLParse(input, {\n            url: this._url,\n            stateOverride: \"fragment\"\n        });\n    }\n    toJSON() {\n        return this.href;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/URL.js":
/*!********************************************!*\
  !*** ./node_modules/whatwg-url/lib/URL.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(rsc)/./node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(rsc)/./node_modules/whatwg-url/lib/URL-impl.js\");\nconst impl = utils.implSymbol;\nfunction URL(url) {\n    if (!this || this[impl] || !(this instanceof URL)) {\n        throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n    }\n    if (arguments.length < 1) {\n        throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 2; ++i){\n        args[i] = arguments[i];\n    }\n    args[0] = conversions[\"USVString\"](args[0]);\n    if (args[1] !== undefined) {\n        args[1] = conversions[\"USVString\"](args[1]);\n    }\n    module.exports.setup(this, args);\n}\nURL.prototype.toJSON = function toJSON() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 0; ++i){\n        args[i] = arguments[i];\n    }\n    return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n    get () {\n        return this[impl].href;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].href = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nURL.prototype.toString = function() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    return this.href;\n};\nObject.defineProperty(URL.prototype, \"origin\", {\n    get () {\n        return this[impl].origin;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"protocol\", {\n    get () {\n        return this[impl].protocol;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].protocol = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"username\", {\n    get () {\n        return this[impl].username;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].username = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"password\", {\n    get () {\n        return this[impl].password;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].password = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"host\", {\n    get () {\n        return this[impl].host;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].host = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hostname\", {\n    get () {\n        return this[impl].hostname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hostname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"port\", {\n    get () {\n        return this[impl].port;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].port = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"pathname\", {\n    get () {\n        return this[impl].pathname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].pathname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"search\", {\n    get () {\n        return this[impl].search;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].search = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hash\", {\n    get () {\n        return this[impl].hash;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hash = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nmodule.exports = {\n    is (obj) {\n        return !!obj && obj[impl] instanceof Impl.implementation;\n    },\n    create (constructorArgs, privateData) {\n        let obj = Object.create(URL.prototype);\n        this.setup(obj, constructorArgs, privateData);\n        return obj;\n    },\n    setup (obj, constructorArgs, privateData) {\n        if (!privateData) privateData = {};\n        privateData.wrapper = obj;\n        obj[impl] = new Impl.implementation(constructorArgs, privateData);\n        obj[impl][utils.wrapperSymbol] = obj;\n    },\n    interface: URL,\n    expose: {\n        Window: {\n            URL: URL\n        },\n        Worker: {\n            URL: URL\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/public-api.js":
/*!***************************************************!*\
  !*** ./node_modules/whatwg-url/lib/public-api.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.URL = __webpack_require__(/*! ./URL */ \"(rsc)/./node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViQSx5R0FBd0M7QUFDeENBLDhJQUFrRTtBQUNsRUEsMEpBQThFO0FBQzlFQSxnSkFBb0U7QUFDcEVBLGtKQUFzRTtBQUN0RUEsa0pBQXNFO0FBQ3RFQSxnSkFBb0U7QUFDcEVBLHNKQUEwRTtBQUMxRUEsc0lBQTBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmhlZW1kaW5lLy4vbm9kZV9tb2R1bGVzL3doYXR3Zy11cmwvbGliL3B1YmxpYy1hcGkuanM/MmQ4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuZXhwb3J0cy5VUkwgPSByZXF1aXJlKFwiLi9VUkxcIikuaW50ZXJmYWNlO1xuZXhwb3J0cy5zZXJpYWxpemVVUkwgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVVUkw7XG5leHBvcnRzLnNlcmlhbGl6ZVVSTE9yaWdpbiA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZVVSTE9yaWdpbjtcbmV4cG9ydHMuYmFzaWNVUkxQYXJzZSA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLmJhc2ljVVJMUGFyc2U7XG5leHBvcnRzLnNldFRoZVVzZXJuYW1lID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2V0VGhlVXNlcm5hbWU7XG5leHBvcnRzLnNldFRoZVBhc3N3b3JkID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2V0VGhlUGFzc3dvcmQ7XG5leHBvcnRzLnNlcmlhbGl6ZUhvc3QgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVIb3N0O1xuZXhwb3J0cy5zZXJpYWxpemVJbnRlZ2VyID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplSW50ZWdlcjtcbmV4cG9ydHMucGFyc2VVUkwgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5wYXJzZVVSTDtcbiJdLCJuYW1lcyI6WyJleHBvcnRzIiwiVVJMIiwicmVxdWlyZSIsImludGVyZmFjZSIsInNlcmlhbGl6ZVVSTCIsInNlcmlhbGl6ZVVSTE9yaWdpbiIsImJhc2ljVVJMUGFyc2UiLCJzZXRUaGVVc2VybmFtZSIsInNldFRoZVBhc3N3b3JkIiwic2VyaWFsaXplSG9zdCIsInNlcmlhbGl6ZUludGVnZXIiLCJwYXJzZVVSTCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js":
/*!**********************************************************!*\
  !*** ./node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\nconst tr46 = __webpack_require__(/*! tr46 */ \"(rsc)/./node_modules/tr46/index.js\");\nconst specialSchemes = {\n    ftp: 21,\n    file: null,\n    gopher: 70,\n    http: 80,\n    https: 443,\n    ws: 80,\n    wss: 443\n};\nconst failure = Symbol(\"failure\");\nfunction countSymbols(str) {\n    return punycode.ucs2.decode(str).length;\n}\nfunction at(input, idx) {\n    const c = input[idx];\n    return isNaN(c) ? undefined : String.fromCodePoint(c);\n}\nfunction isASCIIDigit(c) {\n    return c >= 0x30 && c <= 0x39;\n}\nfunction isASCIIAlpha(c) {\n    return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;\n}\nfunction isASCIIAlphanumeric(c) {\n    return isASCIIAlpha(c) || isASCIIDigit(c);\n}\nfunction isASCIIHex(c) {\n    return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;\n}\nfunction isSingleDot(buffer) {\n    return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\n}\nfunction isDoubleDot(buffer) {\n    buffer = buffer.toLowerCase();\n    return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\n}\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\n    return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\n}\nfunction isWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\n}\nfunction isNormalizedWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\n}\nfunction containsForbiddenHostCodePoint(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction isSpecialScheme(scheme) {\n    return specialSchemes[scheme] !== undefined;\n}\nfunction isSpecial(url) {\n    return isSpecialScheme(url.scheme);\n}\nfunction defaultPort(scheme) {\n    return specialSchemes[scheme];\n}\nfunction percentEncode(c) {\n    let hex = c.toString(16).toUpperCase();\n    if (hex.length === 1) {\n        hex = \"0\" + hex;\n    }\n    return \"%\" + hex;\n}\nfunction utf8PercentEncode(c) {\n    const buf = new Buffer(c);\n    let str = \"\";\n    for(let i = 0; i < buf.length; ++i){\n        str += percentEncode(buf[i]);\n    }\n    return str;\n}\nfunction utf8PercentDecode(str) {\n    const input = new Buffer(str);\n    const output = [];\n    for(let i = 0; i < input.length; ++i){\n        if (input[i] !== 37) {\n            output.push(input[i]);\n        } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\n            output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\n            i += 2;\n        } else {\n            output.push(input[i]);\n        }\n    }\n    return new Buffer(output).toString();\n}\nfunction isC0ControlPercentEncode(c) {\n    return c <= 0x1F || c > 0x7E;\n}\nconst extraPathPercentEncodeSet = new Set([\n    32,\n    34,\n    35,\n    60,\n    62,\n    63,\n    96,\n    123,\n    125\n]);\nfunction isPathPercentEncode(c) {\n    return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\n}\nconst extraUserinfoPercentEncodeSet = new Set([\n    47,\n    58,\n    59,\n    61,\n    64,\n    91,\n    92,\n    93,\n    94,\n    124\n]);\nfunction isUserinfoPercentEncode(c) {\n    return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\n}\nfunction percentEncodeChar(c, encodeSetPredicate) {\n    const cStr = String.fromCodePoint(c);\n    if (encodeSetPredicate(c)) {\n        return utf8PercentEncode(cStr);\n    }\n    return cStr;\n}\nfunction parseIPv4Number(input) {\n    let R = 10;\n    if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\n        input = input.substring(2);\n        R = 16;\n    } else if (input.length >= 2 && input.charAt(0) === \"0\") {\n        input = input.substring(1);\n        R = 8;\n    }\n    if (input === \"\") {\n        return 0;\n    }\n    const regex = R === 10 ? /[^0-9]/ : R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/;\n    if (regex.test(input)) {\n        return failure;\n    }\n    return parseInt(input, R);\n}\nfunction parseIPv4(input) {\n    const parts = input.split(\".\");\n    if (parts[parts.length - 1] === \"\") {\n        if (parts.length > 1) {\n            parts.pop();\n        }\n    }\n    if (parts.length > 4) {\n        return input;\n    }\n    const numbers = [];\n    for (const part of parts){\n        if (part === \"\") {\n            return input;\n        }\n        const n = parseIPv4Number(part);\n        if (n === failure) {\n            return input;\n        }\n        numbers.push(n);\n    }\n    for(let i = 0; i < numbers.length - 1; ++i){\n        if (numbers[i] > 255) {\n            return failure;\n        }\n    }\n    if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\n        return failure;\n    }\n    let ipv4 = numbers.pop();\n    let counter = 0;\n    for (const n of numbers){\n        ipv4 += n * Math.pow(256, 3 - counter);\n        ++counter;\n    }\n    return ipv4;\n}\nfunction serializeIPv4(address) {\n    let output = \"\";\n    let n = address;\n    for(let i = 1; i <= 4; ++i){\n        output = String(n % 256) + output;\n        if (i !== 4) {\n            output = \".\" + output;\n        }\n        n = Math.floor(n / 256);\n    }\n    return output;\n}\nfunction parseIPv6(input) {\n    const address = [\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0\n    ];\n    let pieceIndex = 0;\n    let compress = null;\n    let pointer = 0;\n    input = punycode.ucs2.decode(input);\n    if (input[pointer] === 58) {\n        if (input[pointer + 1] !== 58) {\n            return failure;\n        }\n        pointer += 2;\n        ++pieceIndex;\n        compress = pieceIndex;\n    }\n    while(pointer < input.length){\n        if (pieceIndex === 8) {\n            return failure;\n        }\n        if (input[pointer] === 58) {\n            if (compress !== null) {\n                return failure;\n            }\n            ++pointer;\n            ++pieceIndex;\n            compress = pieceIndex;\n            continue;\n        }\n        let value = 0;\n        let length = 0;\n        while(length < 4 && isASCIIHex(input[pointer])){\n            value = value * 0x10 + parseInt(at(input, pointer), 16);\n            ++pointer;\n            ++length;\n        }\n        if (input[pointer] === 46) {\n            if (length === 0) {\n                return failure;\n            }\n            pointer -= length;\n            if (pieceIndex > 6) {\n                return failure;\n            }\n            let numbersSeen = 0;\n            while(input[pointer] !== undefined){\n                let ipv4Piece = null;\n                if (numbersSeen > 0) {\n                    if (input[pointer] === 46 && numbersSeen < 4) {\n                        ++pointer;\n                    } else {\n                        return failure;\n                    }\n                }\n                if (!isASCIIDigit(input[pointer])) {\n                    return failure;\n                }\n                while(isASCIIDigit(input[pointer])){\n                    const number = parseInt(at(input, pointer));\n                    if (ipv4Piece === null) {\n                        ipv4Piece = number;\n                    } else if (ipv4Piece === 0) {\n                        return failure;\n                    } else {\n                        ipv4Piece = ipv4Piece * 10 + number;\n                    }\n                    if (ipv4Piece > 255) {\n                        return failure;\n                    }\n                    ++pointer;\n                }\n                address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\n                ++numbersSeen;\n                if (numbersSeen === 2 || numbersSeen === 4) {\n                    ++pieceIndex;\n                }\n            }\n            if (numbersSeen !== 4) {\n                return failure;\n            }\n            break;\n        } else if (input[pointer] === 58) {\n            ++pointer;\n            if (input[pointer] === undefined) {\n                return failure;\n            }\n        } else if (input[pointer] !== undefined) {\n            return failure;\n        }\n        address[pieceIndex] = value;\n        ++pieceIndex;\n    }\n    if (compress !== null) {\n        let swaps = pieceIndex - compress;\n        pieceIndex = 7;\n        while(pieceIndex !== 0 && swaps > 0){\n            const temp = address[compress + swaps - 1];\n            address[compress + swaps - 1] = address[pieceIndex];\n            address[pieceIndex] = temp;\n            --pieceIndex;\n            --swaps;\n        }\n    } else if (compress === null && pieceIndex !== 8) {\n        return failure;\n    }\n    return address;\n}\nfunction serializeIPv6(address) {\n    let output = \"\";\n    const seqResult = findLongestZeroSequence(address);\n    const compress = seqResult.idx;\n    let ignore0 = false;\n    for(let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex){\n        if (ignore0 && address[pieceIndex] === 0) {\n            continue;\n        } else if (ignore0) {\n            ignore0 = false;\n        }\n        if (compress === pieceIndex) {\n            const separator = pieceIndex === 0 ? \"::\" : \":\";\n            output += separator;\n            ignore0 = true;\n            continue;\n        }\n        output += address[pieceIndex].toString(16);\n        if (pieceIndex !== 7) {\n            output += \":\";\n        }\n    }\n    return output;\n}\nfunction parseHost(input, isSpecialArg) {\n    if (input[0] === \"[\") {\n        if (input[input.length - 1] !== \"]\") {\n            return failure;\n        }\n        return parseIPv6(input.substring(1, input.length - 1));\n    }\n    if (!isSpecialArg) {\n        return parseOpaqueHost(input);\n    }\n    const domain = utf8PercentDecode(input);\n    const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\n    if (asciiDomain === null) {\n        return failure;\n    }\n    if (containsForbiddenHostCodePoint(asciiDomain)) {\n        return failure;\n    }\n    const ipv4Host = parseIPv4(asciiDomain);\n    if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\n        return ipv4Host;\n    }\n    return asciiDomain;\n}\nfunction parseOpaqueHost(input) {\n    if (containsForbiddenHostCodePointExcludingPercent(input)) {\n        return failure;\n    }\n    let output = \"\";\n    const decoded = punycode.ucs2.decode(input);\n    for(let i = 0; i < decoded.length; ++i){\n        output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\n    }\n    return output;\n}\nfunction findLongestZeroSequence(arr) {\n    let maxIdx = null;\n    let maxLen = 1; // only find elements > 1\n    let currStart = null;\n    let currLen = 0;\n    for(let i = 0; i < arr.length; ++i){\n        if (arr[i] !== 0) {\n            if (currLen > maxLen) {\n                maxIdx = currStart;\n                maxLen = currLen;\n            }\n            currStart = null;\n            currLen = 0;\n        } else {\n            if (currStart === null) {\n                currStart = i;\n            }\n            ++currLen;\n        }\n    }\n    // if trailing zeros\n    if (currLen > maxLen) {\n        maxIdx = currStart;\n        maxLen = currLen;\n    }\n    return {\n        idx: maxIdx,\n        len: maxLen\n    };\n}\nfunction serializeHost(host) {\n    if (typeof host === \"number\") {\n        return serializeIPv4(host);\n    }\n    // IPv6 serializer\n    if (host instanceof Array) {\n        return \"[\" + serializeIPv6(host) + \"]\";\n    }\n    return host;\n}\nfunction trimControlChars(url) {\n    return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\n}\nfunction trimTabAndNewline(url) {\n    return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\n}\nfunction shortenPath(url) {\n    const path = url.path;\n    if (path.length === 0) {\n        return;\n    }\n    if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\n        return;\n    }\n    path.pop();\n}\nfunction includesCredentials(url) {\n    return url.username !== \"\" || url.password !== \"\";\n}\nfunction cannotHaveAUsernamePasswordPort(url) {\n    return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\n}\nfunction isNormalizedWindowsDriveLetter(string) {\n    return /^[A-Za-z]:$/.test(string);\n}\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\n    this.pointer = 0;\n    this.input = input;\n    this.base = base || null;\n    this.encodingOverride = encodingOverride || \"utf-8\";\n    this.stateOverride = stateOverride;\n    this.url = url;\n    this.failure = false;\n    this.parseError = false;\n    if (!this.url) {\n        this.url = {\n            scheme: \"\",\n            username: \"\",\n            password: \"\",\n            host: null,\n            port: null,\n            path: [],\n            query: null,\n            fragment: null,\n            cannotBeABaseURL: false\n        };\n        const res = trimControlChars(this.input);\n        if (res !== this.input) {\n            this.parseError = true;\n        }\n        this.input = res;\n    }\n    const res = trimTabAndNewline(this.input);\n    if (res !== this.input) {\n        this.parseError = true;\n    }\n    this.input = res;\n    this.state = stateOverride || \"scheme start\";\n    this.buffer = \"\";\n    this.atFlag = false;\n    this.arrFlag = false;\n    this.passwordTokenSeenFlag = false;\n    this.input = punycode.ucs2.decode(this.input);\n    for(; this.pointer <= this.input.length; ++this.pointer){\n        const c = this.input[this.pointer];\n        const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\n        // exec state machine\n        const ret = this[\"parse \" + this.state](c, cStr);\n        if (!ret) {\n            break; // terminate algorithm\n        } else if (ret === failure) {\n            this.failure = true;\n            break;\n        }\n    }\n}\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\n    if (isASCIIAlpha(c)) {\n        this.buffer += cStr.toLowerCase();\n        this.state = \"scheme\";\n    } else if (!this.stateOverride) {\n        this.state = \"no scheme\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\n    if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\n        this.buffer += cStr.toLowerCase();\n    } else if (c === 58) {\n        if (this.stateOverride) {\n            if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\n                return false;\n            }\n            if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\n                return false;\n            }\n        }\n        this.url.scheme = this.buffer;\n        this.buffer = \"\";\n        if (this.stateOverride) {\n            return false;\n        }\n        if (this.url.scheme === \"file\") {\n            if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\n                this.parseError = true;\n            }\n            this.state = \"file\";\n        } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\n            this.state = \"special relative or authority\";\n        } else if (isSpecial(this.url)) {\n            this.state = \"special authority slashes\";\n        } else if (this.input[this.pointer + 1] === 47) {\n            this.state = \"path or authority\";\n            ++this.pointer;\n        } else {\n            this.url.cannotBeABaseURL = true;\n            this.url.path.push(\"\");\n            this.state = \"cannot-be-a-base-URL path\";\n        }\n    } else if (!this.stateOverride) {\n        this.buffer = \"\";\n        this.state = \"no scheme\";\n        this.pointer = -1;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\n    if (this.base === null || this.base.cannotBeABaseURL && c !== 35) {\n        return failure;\n    } else if (this.base.cannotBeABaseURL && c === 35) {\n        this.url.scheme = this.base.scheme;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.url.cannotBeABaseURL = true;\n        this.state = \"fragment\";\n    } else if (this.base.scheme === \"file\") {\n        this.state = \"file\";\n        --this.pointer;\n    } else {\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\n    if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\n    this.url.scheme = this.base.scheme;\n    if (isNaN(c)) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n    } else if (c === 47) {\n        this.state = \"relative slash\";\n    } else if (c === 63) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (isSpecial(this.url) && c === 92) {\n        this.parseError = true;\n        this.state = \"relative slash\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice(0, this.base.path.length - 1);\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\n    if (isSpecial(this.url) && (c === 47 || c === 92)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"special authority ignore slashes\";\n    } else if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"special authority ignore slashes\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\n    if (c !== 47 && c !== 92) {\n        this.state = \"authority\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\n    if (c === 64) {\n        this.parseError = true;\n        if (this.atFlag) {\n            this.buffer = \"%40\" + this.buffer;\n        }\n        this.atFlag = true;\n        // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\n        const len = countSymbols(this.buffer);\n        for(let pointer = 0; pointer < len; ++pointer){\n            const codePoint = this.buffer.codePointAt(pointer);\n            if (codePoint === 58 && !this.passwordTokenSeenFlag) {\n                this.passwordTokenSeenFlag = true;\n                continue;\n            }\n            const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\n            if (this.passwordTokenSeenFlag) {\n                this.url.password += encodedCodePoints;\n            } else {\n                this.url.username += encodedCodePoints;\n            }\n        }\n        this.buffer = \"\";\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        if (this.atFlag && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        this.pointer -= countSymbols(this.buffer) + 1;\n        this.buffer = \"\";\n        this.state = \"host\";\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse hostname\"] = URLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\n    if (this.stateOverride && this.url.scheme === \"file\") {\n        --this.pointer;\n        this.state = \"file host\";\n    } else if (c === 58 && !this.arrFlag) {\n        if (this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"port\";\n        if (this.stateOverride === \"hostname\") {\n            return false;\n        }\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        --this.pointer;\n        if (isSpecial(this.url) && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        } else if (this.stateOverride && this.buffer === \"\" && (includesCredentials(this.url) || this.url.port !== null)) {\n            this.parseError = true;\n            return false;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"path start\";\n        if (this.stateOverride) {\n            return false;\n        }\n    } else {\n        if (c === 91) {\n            this.arrFlag = true;\n        } else if (c === 93) {\n            this.arrFlag = false;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\n    if (isASCIIDigit(c)) {\n        this.buffer += cStr;\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92 || this.stateOverride) {\n        if (this.buffer !== \"\") {\n            const port = parseInt(this.buffer);\n            if (port > Math.pow(2, 16) - 1) {\n                this.parseError = true;\n                return failure;\n            }\n            this.url.port = port === defaultPort(this.url.scheme) ? null : port;\n            this.buffer = \"\";\n        }\n        if (this.stateOverride) {\n            return false;\n        }\n        this.state = \"path start\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nconst fileOtherwiseCodePoints = new Set([\n    47,\n    92,\n    63,\n    35\n]);\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\n    this.url.scheme = \"file\";\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file slash\";\n    } else if (this.base !== null && this.base.scheme === \"file\") {\n        if (isNaN(c)) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n        } else if (c === 63) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = \"\";\n            this.state = \"query\";\n        } else if (c === 35) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        } else {\n            if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\n            !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) || this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\n            !fileOtherwiseCodePoints.has(this.input[this.pointer + 2])) {\n                this.url.host = this.base.host;\n                this.url.path = this.base.path.slice();\n                shortenPath(this.url);\n            } else {\n                this.parseError = true;\n            }\n            this.state = \"path\";\n            --this.pointer;\n        }\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file host\";\n    } else {\n        if (this.base !== null && this.base.scheme === \"file\") {\n            if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\n                this.url.path.push(this.base.path[0]);\n            } else {\n                this.url.host = this.base.host;\n            }\n        }\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\n    if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\n        --this.pointer;\n        if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\n            this.parseError = true;\n            this.state = \"path\";\n        } else if (this.buffer === \"\") {\n            this.url.host = \"\";\n            if (this.stateOverride) {\n                return false;\n            }\n            this.state = \"path start\";\n        } else {\n            let host = parseHost(this.buffer, isSpecial(this.url));\n            if (host === failure) {\n                return failure;\n            }\n            if (host === \"localhost\") {\n                host = \"\";\n            }\n            this.url.host = host;\n            if (this.stateOverride) {\n                return false;\n            }\n            this.buffer = \"\";\n            this.state = \"path start\";\n        }\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\n    if (isSpecial(this.url)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"path\";\n        if (c !== 47 && c !== 92) {\n            --this.pointer;\n        }\n    } else if (!this.stateOverride && c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (!this.stateOverride && c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (c !== undefined) {\n        this.state = \"path\";\n        if (c !== 47) {\n            --this.pointer;\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\n    if (isNaN(c) || c === 47 || isSpecial(this.url) && c === 92 || !this.stateOverride && (c === 63 || c === 35)) {\n        if (isSpecial(this.url) && c === 92) {\n            this.parseError = true;\n        }\n        if (isDoubleDot(this.buffer)) {\n            shortenPath(this.url);\n            if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\n                this.url.path.push(\"\");\n            }\n        } else if (isSingleDot(this.buffer) && c !== 47 && !(isSpecial(this.url) && c === 92)) {\n            this.url.path.push(\"\");\n        } else if (!isSingleDot(this.buffer)) {\n            if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\n                if (this.url.host !== \"\" && this.url.host !== null) {\n                    this.parseError = true;\n                    this.url.host = \"\";\n                }\n                this.buffer = this.buffer[0] + \":\";\n            }\n            this.url.path.push(this.buffer);\n        }\n        this.buffer = \"\";\n        if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\n            while(this.url.path.length > 1 && this.url.path[0] === \"\"){\n                this.parseError = true;\n                this.url.path.shift();\n            }\n        }\n        if (c === 63) {\n            this.url.query = \"\";\n            this.state = \"query\";\n        }\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += percentEncodeChar(c, isPathPercentEncode);\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\n    if (c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else {\n        // TODO: Add: not a URL code point\n        if (!isNaN(c) && c !== 37) {\n            this.parseError = true;\n        }\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        if (!isNaN(c)) {\n            this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\n    if (isNaN(c) || !this.stateOverride && c === 35) {\n        if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\n            this.encodingOverride = \"utf-8\";\n        }\n        const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\n        for(let i = 0; i < buffer.length; ++i){\n            if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 || buffer[i] === 0x3C || buffer[i] === 0x3E) {\n                this.url.query += percentEncode(buffer[i]);\n            } else {\n                this.url.query += String.fromCodePoint(buffer[i]);\n            }\n        }\n        this.buffer = \"\";\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\n    if (isNaN(c)) {} else if (c === 0x0) {\n        this.parseError = true;\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\n    }\n    return true;\n};\nfunction serializeURL(url, excludeFragment) {\n    let output = url.scheme + \":\";\n    if (url.host !== null) {\n        output += \"//\";\n        if (url.username !== \"\" || url.password !== \"\") {\n            output += url.username;\n            if (url.password !== \"\") {\n                output += \":\" + url.password;\n            }\n            output += \"@\";\n        }\n        output += serializeHost(url.host);\n        if (url.port !== null) {\n            output += \":\" + url.port;\n        }\n    } else if (url.host === null && url.scheme === \"file\") {\n        output += \"//\";\n    }\n    if (url.cannotBeABaseURL) {\n        output += url.path[0];\n    } else {\n        for (const string of url.path){\n            output += \"/\" + string;\n        }\n    }\n    if (url.query !== null) {\n        output += \"?\" + url.query;\n    }\n    if (!excludeFragment && url.fragment !== null) {\n        output += \"#\" + url.fragment;\n    }\n    return output;\n}\nfunction serializeOrigin(tuple) {\n    let result = tuple.scheme + \"://\";\n    result += serializeHost(tuple.host);\n    if (tuple.port !== null) {\n        result += \":\" + tuple.port;\n    }\n    return result;\n}\nmodule.exports.serializeURL = serializeURL;\nmodule.exports.serializeURLOrigin = function(url) {\n    // https://url.spec.whatwg.org/#concept-url-origin\n    switch(url.scheme){\n        case \"blob\":\n            try {\n                return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\n            } catch (e) {\n                // serializing an opaque origin returns \"null\"\n                return \"null\";\n            }\n        case \"ftp\":\n        case \"gopher\":\n        case \"http\":\n        case \"https\":\n        case \"ws\":\n        case \"wss\":\n            return serializeOrigin({\n                scheme: url.scheme,\n                host: url.host,\n                port: url.port\n            });\n        case \"file\":\n            // spec says \"exercise to the reader\", chrome says \"file://\"\n            return \"file://\";\n        default:\n            // serializing an opaque origin returns \"null\"\n            return \"null\";\n    }\n};\nmodule.exports.basicURLParse = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\n    if (usm.failure) {\n        return \"failure\";\n    }\n    return usm.url;\n};\nmodule.exports.setTheUsername = function(url, username) {\n    url.username = \"\";\n    const decoded = punycode.ucs2.decode(username);\n    for(let i = 0; i < decoded.length; ++i){\n        url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.setThePassword = function(url, password) {\n    url.password = \"\";\n    const decoded = punycode.ucs2.decode(password);\n    for(let i = 0; i < decoded.length; ++i){\n        url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.serializeHost = serializeHost;\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\nmodule.exports.serializeInteger = function(integer) {\n    return String(integer);\n};\nmodule.exports.parseURL = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    // We don't handle blobs, so this just delegates:\n    return module.exports.basicURLParse(input, {\n        baseURL: options.baseURL,\n        encodingOverride: options.encodingOverride\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/whatwg-url/lib/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/whatwg-url/lib/utils.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\nmodule.exports.mixin = function mixin(target, source) {\n    const keys = Object.getOwnPropertyNames(source);\n    for(let i = 0; i < keys.length; ++i){\n        Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n    }\n};\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\nmodule.exports.wrapperForImpl = function(impl) {\n    return impl[module.exports.wrapperSymbol];\n};\nmodule.exports.implForWrapper = function(wrapper) {\n    return wrapper[module.exports.implSymbol];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;