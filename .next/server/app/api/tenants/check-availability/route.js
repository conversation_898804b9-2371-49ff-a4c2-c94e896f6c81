/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tenants/check-availability/route";
exports.ids = ["app/api/tenants/check-availability/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftenants%2Fcheck-availability%2Froute&page=%2Fapi%2Ftenants%2Fcheck-availability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftenants%2Fcheck-availability%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftenants%2Fcheck-availability%2Froute&page=%2Fapi%2Ftenants%2Fcheck-availability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftenants%2Fcheck-availability%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_shiva_Desktop_BHEEMDINE_src_app_api_tenants_check_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tenants/check-availability/route.ts */ \"(rsc)/./src/app/api/tenants/check-availability/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tenants/check-availability/route\",\n        pathname: \"/api/tenants/check-availability\",\n        filename: \"route\",\n        bundlePath: \"app/api/tenants/check-availability/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/BHEEMDINE/src/app/api/tenants/check-availability/route.ts\",\n    nextConfigOutput,\n    userland: _Users_shiva_Desktop_BHEEMDINE_src_app_api_tenants_check_availability_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/tenants/check-availability/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZ0ZW5hbnRzJTJGY2hlY2stYXZhaWxhYmlsaXR5JTJGcm91dGUmcGFnZT0lMkZhcGklMkZ0ZW5hbnRzJTJGY2hlY2stYXZhaWxhYmlsaXR5JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGdGVuYW50cyUyRmNoZWNrLWF2YWlsYWJpbGl0eSUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRnNoaXZhJTJGRGVza3RvcCUyRkJIRUVNRElORSUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZzaGl2YSUyRkRlc2t0b3AlMkZCSEVFTURJTkUmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDOEI7QUFDM0c7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1R0FBdUc7QUFDL0c7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUM2Sjs7QUFFN0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvPzIzZjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL1VzZXJzL3NoaXZhL0Rlc2t0b3AvQkhFRU1ESU5FL3NyYy9hcHAvYXBpL3RlbmFudHMvY2hlY2stYXZhaWxhYmlsaXR5L3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS90ZW5hbnRzL2NoZWNrLWF2YWlsYWJpbGl0eS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3RlbmFudHMvY2hlY2stYXZhaWxhYmlsaXR5XCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS90ZW5hbnRzL2NoZWNrLWF2YWlsYWJpbGl0eS9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy9zaGl2YS9EZXNrdG9wL0JIRUVNRElORS9zcmMvYXBwL2FwaS90ZW5hbnRzL2NoZWNrLWF2YWlsYWJpbGl0eS9yb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS90ZW5hbnRzL2NoZWNrLWF2YWlsYWJpbGl0eS9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftenants%2Fcheck-availability%2Froute&page=%2Fapi%2Ftenants%2Fcheck-availability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftenants%2Fcheck-availability%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/tenants/check-availability/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/tenants/check-availability/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_api_tenant_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/tenant-service */ \"(rsc)/./src/lib/api/tenant-service.ts\");\n/**\n * Check tenant slug and email availability\n * GET /api/tenants/check-availability?slug=test&email=<EMAIL>\n */ \n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const slug = searchParams.get(\"slug\");\n        const email = searchParams.get(\"email\");\n        if (!slug && !email) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Either slug or email parameter is required\",\n                error_code: \"MISSING_PARAMETERS\"\n            }, {\n                status: 400\n            });\n        }\n        // Check availability\n        const results = await _lib_api_tenant_service__WEBPACK_IMPORTED_MODULE_1__.TenantService.checkUniqueness(email || undefined, slug || undefined);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(results);\n    } catch (error) {\n        console.error(\"Availability check error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Failed to check availability\",\n            error_code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle unsupported methods\nasync function POST() {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        success: false,\n        error: \"Method not allowed\",\n        error_code: \"METHOD_NOT_ALLOWED\"\n    }, {\n        status: 405\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tenants/check-availability/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/database.ts":
/*!*********************************!*\
  !*** ./src/lib/api/database.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Database client configuration for API routes\n * Provides database connection and query utilities\n */ \n// Singleton pattern for Prisma client to prevent connection issues\nconst prisma = globalThis.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\",\n        \"info\",\n        \"warn\",\n        \"error\"\n    ]\n});\nif (true) {\n    globalThis.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FwaS9kYXRhYmFzZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7OztDQUdDLEdBRTZDO0FBTTlDLG1FQUFtRTtBQUM1RCxNQUFNQyxTQUFTQyxXQUFXRCxNQUFNLElBQUksSUFBSUQsd0RBQVlBLENBQUM7SUFDMURHLEtBQUs7UUFBQztRQUFTO1FBQVE7UUFBUTtLQUFRO0FBQ3pDLEdBQUc7QUFFSCxJQUFJQyxJQUF5QixFQUFjO0lBQ3pDRixXQUFXRCxNQUFNLEdBQUdBO0FBQ3RCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9zcmMvbGliL2FwaS9kYXRhYmFzZS50cz9iM2YyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGF0YWJhc2UgY2xpZW50IGNvbmZpZ3VyYXRpb24gZm9yIEFQSSByb3V0ZXNcbiAqIFByb3ZpZGVzIGRhdGFiYXNlIGNvbm5lY3Rpb24gYW5kIHF1ZXJ5IHV0aWxpdGllc1xuICovXG5cbmltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuZGVjbGFyZSBnbG9iYWwge1xuICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbi8vIFNpbmdsZXRvbiBwYXR0ZXJuIGZvciBQcmlzbWEgY2xpZW50IHRvIHByZXZlbnQgY29ubmVjdGlvbiBpc3N1ZXNcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxUaGlzLnByaXNtYSB8fCBuZXcgUHJpc21hQ2xpZW50KHtcbiAgbG9nOiBbJ3F1ZXJ5JywgJ2luZm8nLCAnd2FybicsICdlcnJvciddLFxufSk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbFRoaXMucHJpc21hID0gcHJpc21hO1xufVxuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7Il0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsInByaXNtYSIsImdsb2JhbFRoaXMiLCJsb2ciLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/supabase-server.ts":
/*!****************************************!*\
  !*** ./src/lib/api/supabase-server.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthUser: () => (/* binding */ createAuthUser),\n/* harmony export */   generateUserToken: () => (/* binding */ generateUserToken),\n/* harmony export */   isEmailInUse: () => (/* binding */ isEmailInUse),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Server-side Supabase client for API routes\n * Handles user creation and authentication operations\n */ \nif (false) {}\nif (!process.env.SUPABASE_SERVICE_ROLE_KEY) {\n    throw new Error(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable\");\n}\n// Create admin client with service role key for user management\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://placeholder.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n/**\n * Create a new user in Supabase Auth\n */ async function createAuthUser(email, password, metadata = {}) {\n    try {\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            email_confirm: true,\n            user_metadata: metadata\n        });\n        if (error) {\n            throw error;\n        }\n        return {\n            success: true,\n            user: data.user,\n            auth_user_id: data.user?.id\n        };\n    } catch (error) {\n        console.error(\"Supabase user creation error:\", error);\n        // Handle specific error types\n        if (error.message?.includes(\"already registered\")) {\n            throw new Error(\"Email address is already registered\");\n        }\n        if (error.message?.includes(\"password\")) {\n            throw new Error(\"Password does not meet security requirements\");\n        }\n        throw new Error(\"Failed to create user account\");\n    }\n}\n/**\n * Generate a JWT token for the user\n */ async function generateUserToken(authUserId) {\n    try {\n        // For now, return a placeholder token\n        // In production, you'd generate a proper JWT with custom claims\n        return `auth_token_${authUserId}_${Date.now()}`;\n    } catch (error) {\n        console.error(\"Token generation error:\", error);\n        throw new Error(\"Failed to generate authentication token\");\n    }\n}\n/**\n * Verify if an email is already in use\n */ async function isEmailInUse(email) {\n    try {\n        const { data, error } = await supabaseAdmin.auth.admin.listUsers({\n            page: 1,\n            perPage: 1000 // This is not ideal for large user bases, but works for demo\n        });\n        if (error) {\n            console.error(\"Error checking email:\", error);\n            return false; // Assume not in use if we can't check\n        }\n        return data.users.some((user)=>user.email === email);\n    } catch (error) {\n        console.error(\"Error checking email availability:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2FwaS9zdXBhYmFzZS1zZXJ2ZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFb0Q7QUFFckQsSUFBSSxLQUFxQyxFQUFFLEVBRTFDO0FBRUQsSUFBSSxDQUFDQyxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QixFQUFFO0lBQzFDLE1BQU0sSUFBSUQsTUFBTTtBQUNsQjtBQUVBLGdFQUFnRTtBQUN6RCxNQUFNRSxnQkFBZ0JOLG1FQUFZQSxDQUN2Q0MsaUNBQW9DLEVBQ3BDQSxRQUFRQyxHQUFHLENBQUNHLHlCQUF5QixFQUNyQztJQUNFRSxNQUFNO1FBQ0pDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO0lBQ2xCO0FBQ0YsR0FDQTtBQUVGOztDQUVDLEdBQ00sZUFBZUMsZUFBZUMsS0FBYSxFQUFFQyxRQUFnQixFQUFFQyxXQUFnQixDQUFDLENBQUM7SUFDdEYsSUFBSTtRQUNGLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVCxjQUFjQyxJQUFJLENBQUNTLEtBQUssQ0FBQ0MsVUFBVSxDQUFDO1lBQ2hFTjtZQUNBQztZQUNBTSxlQUFlO1lBQ2ZDLGVBQWVOO1FBQ2pCO1FBRUEsSUFBSUUsT0FBTztZQUNULE1BQU1BO1FBQ1I7UUFFQSxPQUFPO1lBQ0xLLFNBQVM7WUFDVEMsTUFBTVAsS0FBS08sSUFBSTtZQUNmQyxjQUFjUixLQUFLTyxJQUFJLEVBQUVFO1FBQzNCO0lBQ0YsRUFBRSxPQUFPUixPQUFZO1FBQ25CUyxRQUFRVCxLQUFLLENBQUMsaUNBQWlDQTtRQUUvQyw4QkFBOEI7UUFDOUIsSUFBSUEsTUFBTVUsT0FBTyxFQUFFQyxTQUFTLHVCQUF1QjtZQUNqRCxNQUFNLElBQUl0QixNQUFNO1FBQ2xCO1FBRUEsSUFBSVcsTUFBTVUsT0FBTyxFQUFFQyxTQUFTLGFBQWE7WUFDdkMsTUFBTSxJQUFJdEIsTUFBTTtRQUNsQjtRQUVBLE1BQU0sSUFBSUEsTUFBTTtJQUNsQjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFldUIsa0JBQWtCQyxVQUFrQjtJQUN4RCxJQUFJO1FBQ0Ysc0NBQXNDO1FBQ3RDLGdFQUFnRTtRQUNoRSxPQUFPLENBQUMsV0FBVyxFQUFFQSxXQUFXLENBQUMsRUFBRUMsS0FBS0MsR0FBRyxHQUFHLENBQUM7SUFDakQsRUFBRSxPQUFPZixPQUFPO1FBQ2RTLFFBQVFULEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU0sSUFBSVgsTUFBTTtJQUNsQjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlMkIsYUFBYXBCLEtBQWE7SUFDOUMsSUFBSTtRQUNGLE1BQU0sRUFBRUcsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVCxjQUFjQyxJQUFJLENBQUNTLEtBQUssQ0FBQ2dCLFNBQVMsQ0FBQztZQUMvREMsTUFBTTtZQUNOQyxTQUFTLEtBQUssNkRBQTZEO1FBQzdFO1FBRUEsSUFBSW5CLE9BQU87WUFDVFMsUUFBUVQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkMsT0FBTyxPQUFPLHNDQUFzQztRQUN0RDtRQUVBLE9BQU9ELEtBQUtxQixLQUFLLENBQUNDLElBQUksQ0FBQ2YsQ0FBQUEsT0FBUUEsS0FBS1YsS0FBSyxLQUFLQTtJQUNoRCxFQUFFLE9BQU9JLE9BQU87UUFDZFMsUUFBUVQsS0FBSyxDQUFDLHNDQUFzQ0E7UUFDcEQsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iaGVlbWRpbmUvLi9zcmMvbGliL2FwaS9zdXBhYmFzZS1zZXJ2ZXIudHM/OTYzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNlcnZlci1zaWRlIFN1cGFiYXNlIGNsaWVudCBmb3IgQVBJIHJvdXRlc1xuICogSGFuZGxlcyB1c2VyIGNyZWF0aW9uIGFuZCBhdXRoZW50aWNhdGlvbiBvcGVyYXRpb25zXG4gKi9cblxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuaWYgKCFwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIE5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBlbnZpcm9ubWVudCB2YXJpYWJsZScpO1xufVxuXG5pZiAoIXByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkpIHtcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIFNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgZW52aXJvbm1lbnQgdmFyaWFibGUnKTtcbn1cblxuLy8gQ3JlYXRlIGFkbWluIGNsaWVudCB3aXRoIHNlcnZpY2Ugcm9sZSBrZXkgZm9yIHVzZXIgbWFuYWdlbWVudFxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlQWRtaW4gPSBjcmVhdGVDbGllbnQoXG4gIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCxcbiAgcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSxcbiAge1xuICAgIGF1dGg6IHtcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGZhbHNlLFxuICAgICAgcGVyc2lzdFNlc3Npb246IGZhbHNlXG4gICAgfVxuICB9XG4pO1xuXG4vKipcbiAqIENyZWF0ZSBhIG5ldyB1c2VyIGluIFN1cGFiYXNlIEF1dGhcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUF1dGhVc2VyKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIG1ldGFkYXRhOiBhbnkgPSB7fSkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW4uYXV0aC5hZG1pbi5jcmVhdGVVc2VyKHtcbiAgICAgIGVtYWlsLFxuICAgICAgcGFzc3dvcmQsXG4gICAgICBlbWFpbF9jb25maXJtOiB0cnVlLCAvLyBBdXRvLWNvbmZpcm0gZW1haWwgZm9yIGFkbWluLWNyZWF0ZWQgdXNlcnNcbiAgICAgIHVzZXJfbWV0YWRhdGE6IG1ldGFkYXRhXG4gICAgfSk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgdXNlcjogZGF0YS51c2VyLFxuICAgICAgYXV0aF91c2VyX2lkOiBkYXRhLnVzZXI/LmlkXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1N1cGFiYXNlIHVzZXIgY3JlYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIFxuICAgIC8vIEhhbmRsZSBzcGVjaWZpYyBlcnJvciB0eXBlc1xuICAgIGlmIChlcnJvci5tZXNzYWdlPy5pbmNsdWRlcygnYWxyZWFkeSByZWdpc3RlcmVkJykpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignRW1haWwgYWRkcmVzcyBpcyBhbHJlYWR5IHJlZ2lzdGVyZWQnKTtcbiAgICB9XG4gICAgXG4gICAgaWYgKGVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdwYXNzd29yZCcpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Bhc3N3b3JkIGRvZXMgbm90IG1lZXQgc2VjdXJpdHkgcmVxdWlyZW1lbnRzJyk7XG4gICAgfVxuICAgIFxuICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGNyZWF0ZSB1c2VyIGFjY291bnQnKTtcbiAgfVxufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgSldUIHRva2VuIGZvciB0aGUgdXNlclxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVVc2VyVG9rZW4oYXV0aFVzZXJJZDogc3RyaW5nKSB7XG4gIHRyeSB7XG4gICAgLy8gRm9yIG5vdywgcmV0dXJuIGEgcGxhY2Vob2xkZXIgdG9rZW5cbiAgICAvLyBJbiBwcm9kdWN0aW9uLCB5b3UnZCBnZW5lcmF0ZSBhIHByb3BlciBKV1Qgd2l0aCBjdXN0b20gY2xhaW1zXG4gICAgcmV0dXJuIGBhdXRoX3Rva2VuXyR7YXV0aFVzZXJJZH1fJHtEYXRlLm5vdygpfWA7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignVG9rZW4gZ2VuZXJhdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgYXV0aGVudGljYXRpb24gdG9rZW4nKTtcbiAgfVxufVxuXG4vKipcbiAqIFZlcmlmeSBpZiBhbiBlbWFpbCBpcyBhbHJlYWR5IGluIHVzZVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gaXNFbWFpbEluVXNlKGVtYWlsOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4ubGlzdFVzZXJzKHtcbiAgICAgIHBhZ2U6IDEsXG4gICAgICBwZXJQYWdlOiAxMDAwIC8vIFRoaXMgaXMgbm90IGlkZWFsIGZvciBsYXJnZSB1c2VyIGJhc2VzLCBidXQgd29ya3MgZm9yIGRlbW9cbiAgICB9KTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgZW1haWw6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlOyAvLyBBc3N1bWUgbm90IGluIHVzZSBpZiB3ZSBjYW4ndCBjaGVja1xuICAgIH1cblxuICAgIHJldHVybiBkYXRhLnVzZXJzLnNvbWUodXNlciA9PiB1c2VyLmVtYWlsID09PSBlbWFpbCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgZW1haWwgYXZhaWxhYmlsaXR5OicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn0iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIkVycm9yIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsInN1cGFiYXNlQWRtaW4iLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiY3JlYXRlQXV0aFVzZXIiLCJlbWFpbCIsInBhc3N3b3JkIiwibWV0YWRhdGEiLCJkYXRhIiwiZXJyb3IiLCJhZG1pbiIsImNyZWF0ZVVzZXIiLCJlbWFpbF9jb25maXJtIiwidXNlcl9tZXRhZGF0YSIsInN1Y2Nlc3MiLCJ1c2VyIiwiYXV0aF91c2VyX2lkIiwiaWQiLCJjb25zb2xlIiwibWVzc2FnZSIsImluY2x1ZGVzIiwiZ2VuZXJhdGVVc2VyVG9rZW4iLCJhdXRoVXNlcklkIiwiRGF0ZSIsIm5vdyIsImlzRW1haWxJblVzZSIsImxpc3RVc2VycyIsInBhZ2UiLCJwZXJQYWdlIiwidXNlcnMiLCJzb21lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/supabase-server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/tenant-service.ts":
/*!***************************************!*\
  !*** ./src/lib/api/tenant-service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantService: () => (/* binding */ TenantService)\n/* harmony export */ });\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/api/database.ts\");\n/* harmony import */ var _supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./supabase-server */ \"(rsc)/./src/lib/api/supabase-server.ts\");\n/**\n * Tenant signup service\n * Handles the complete tenant creation workflow\n */ \n\nclass TenantService {\n    /**\n   * Check if email or slug is already in use\n   */ static async checkUniqueness(email, slug) {\n        const results = {};\n        if (email) {\n            const existingUser = await _database__WEBPACK_IMPORTED_MODULE_0__[\"default\"].adminUser.findUnique({\n                where: {\n                    email\n                }\n            });\n            results.email_available = !existingUser;\n        }\n        if (slug) {\n            const existingTenant = await _database__WEBPACK_IMPORTED_MODULE_0__[\"default\"].tenant.findUnique({\n                where: {\n                    slug\n                }\n            });\n            results.slug_available = !existingTenant;\n        }\n        return results;\n    }\n    /**\n   * Complete tenant signup workflow\n   */ static async createTenantWithAdmin(signupData) {\n        // Step 1: Validate uniqueness\n        const { email_available, slug_available } = await this.checkUniqueness(signupData.email, signupData.tenant_slug);\n        if (email_available === false) {\n            throw new Error(\"Email address is already registered\");\n        }\n        if (slug_available === false) {\n            throw new Error(\"Business URL is already taken\");\n        }\n        // Step 2: Create Supabase auth user\n        const authResult = await (0,_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createAuthUser)(signupData.email, signupData.password, {\n            first_name: signupData.first_name,\n            last_name: signupData.last_name,\n            role: \"OWNER\",\n            tenant_slug: signupData.tenant_slug\n        });\n        if (!authResult.auth_user_id) {\n            throw new Error(\"Failed to create authentication account\");\n        }\n        try {\n            // Step 3: Create tenant and admin user in a transaction\n            const result = await _database__WEBPACK_IMPORTED_MODULE_0__[\"default\"].$transaction(async (tx)=>{\n                // Create tenant\n                const tenant = await tx.tenant.create({\n                    data: {\n                        name: signupData.tenant_name,\n                        slug: signupData.tenant_slug,\n                        email: signupData.email,\n                        phone: signupData.phone || null,\n                        address: signupData.address || null,\n                        city: signupData.city || null,\n                        state: signupData.state || null,\n                        zipCode: signupData.zip_code || null,\n                        status: \"ACTIVE\",\n                        planType: \"TRIAL\",\n                        settings: JSON.stringify({\n                            business_hours: {\n                                monday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                tuesday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                wednesday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                thursday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                friday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                saturday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                },\n                                sunday: {\n                                    open: \"09:00\",\n                                    close: \"22:00\",\n                                    closed: false\n                                }\n                            },\n                            ordering: {\n                                allow_preorders: true,\n                                max_order_advance_days: 7,\n                                min_order_amount: 0,\n                                tax_rate: 0.08\n                            },\n                            notifications: {\n                                email_orders: true,\n                                sms_orders: false,\n                                order_sound: true\n                            }\n                        }),\n                        features: JSON.stringify({\n                            qr_ordering: true,\n                            realtime_orders: true,\n                            analytics: false,\n                            staff_management: true,\n                            menu_management: true,\n                            room_management: true\n                        })\n                    }\n                });\n                // Create admin user\n                const adminUser = await tx.adminUser.create({\n                    data: {\n                        tenantId: tenant.id,\n                        authUserId: authResult.auth_user_id,\n                        email: signupData.email,\n                        firstName: signupData.first_name,\n                        lastName: signupData.last_name,\n                        role: \"OWNER\",\n                        status: \"ACTIVE\",\n                        permissions: JSON.stringify([\n                            \"tenant:manage\",\n                            \"users:manage\",\n                            \"orders:manage\",\n                            \"menu:manage\",\n                            \"rooms:manage\",\n                            \"analytics:view\",\n                            \"settings:manage\"\n                        ]),\n                        preferences: JSON.stringify({\n                            theme: \"light\",\n                            notifications: true,\n                            dashboard_layout: \"default\"\n                        })\n                    }\n                });\n                // Log the signup event\n                await tx.auditLog.create({\n                    data: {\n                        tenantId: tenant.id,\n                        userId: adminUser.id,\n                        action: \"tenant:signup_completed\",\n                        resource: \"tenant\",\n                        resourceId: tenant.id,\n                        metadata: JSON.stringify({\n                            event: \"signup_success\",\n                            plan_type: \"TRIAL\",\n                            timestamp: new Date().toISOString()\n                        })\n                    }\n                });\n                return {\n                    tenant,\n                    adminUser\n                };\n            });\n            // Step 4: Generate JWT token\n            const accessToken = await (0,_supabase_server__WEBPACK_IMPORTED_MODULE_1__.generateUserToken)(authResult.auth_user_id);\n            // Step 5: Return formatted response\n            return {\n                tenant: {\n                    id: result.tenant.id,\n                    name: result.tenant.name,\n                    slug: result.tenant.slug,\n                    email: result.tenant.email,\n                    status: result.tenant.status,\n                    plan_type: result.tenant.planType,\n                    created_at: result.tenant.createdAt.toISOString()\n                },\n                user: {\n                    id: result.adminUser.id,\n                    email: result.adminUser.email,\n                    first_name: result.adminUser.firstName,\n                    last_name: result.adminUser.lastName,\n                    role: result.adminUser.role,\n                    status: result.adminUser.status,\n                    tenant_id: result.adminUser.tenantId,\n                    created_at: result.adminUser.createdAt.toISOString()\n                },\n                access_token: accessToken\n            };\n        } catch (dbError) {\n            console.error(\"Database transaction failed:\", dbError);\n            // TODO: Clean up the Supabase user if database transaction fails\n            // This would require implementing a proper rollback mechanism\n            throw new Error(\"Failed to complete tenant registration\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/tenant-service.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftenants%2Fcheck-availability%2Froute&page=%2Fapi%2Ftenants%2Fcheck-availability%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftenants%2Fcheck-availability%2Froute.ts&appDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fshiva%2FDesktop%2FBHEEMDINE&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();